//
//  LiveMatchWatchView.swift
//  Table Tennis Watch App
//
//  Created by <PERSON><PERSON> on 25/07/2025.
//

import SwiftUI
import WatchKit

struct LiveMatchWatchView: View {
    let match: SharedLiveMatch
    @EnvironmentObject var watchConnectivityManager: WatchConnectivityManager
    
    var body: some View {
        ScrollView {
            VStack(spacing: 12) {
                // Match Header
                VStack(spacing: 4) {
                    Text("Game \(match.gameNumber)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Text("Best of \(match.bestOfGames)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                
                // Games Won Display
                HStack {
                    VStack {
                        Text("\(match.team1GamesWon)")
                            .font(.title2)
                            .fontWeight(.bold)
                        Text("Games")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    VStack {
                        Text("\(match.team2GamesWon)")
                            .font(.title2)
                            .fontWeight(.bold)
                        Text("Games")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.horizontal)
                
                if let game = match.currentGame {
                    // Current Game Scores
                    VStack(spacing: 16) {
                        // Team 1
                        VStack(spacing: 8) {
                            Text(match.team1Name)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                                .minimumScaleFactor(0.8)
                            
                            Button(action: {
                                addPoint(to: SharedTeam.team1)
                            }) {
                                Text("\(game.team1Score)")
                                    .font(.system(size: 36, weight: .bold))
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 60)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(game.currentServer == SharedTeam.team1 ? Color.blue : Color.gray)
                                    )
                            }
                            .buttonStyle(PlainButtonStyle())
                            .disabled(game.isCompleted)
                        }
                        
                        // VS Divider
                        Text("VS")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        
                        // Team 2
                        VStack(spacing: 8) {
                            Text(match.team2Name)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                                .minimumScaleFactor(0.8)
                            
                            Button(action: {
                                addPoint(to: SharedTeam.team2)
                            }) {
                                Text("\(game.team2Score)")
                                    .font(.system(size: 36, weight: .bold))
                                    .foregroundColor(.white)
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 60)
                                    .background(
                                        RoundedRectangle(cornerRadius: 12)
                                            .fill(game.currentServer == SharedTeam.team2 ? Color.red : Color.gray)
                                    )
                            }
                            .buttonStyle(PlainButtonStyle())
                            .disabled(game.isCompleted)
                        }
                    }
                    .padding(.horizontal, 4)
                    
                    // Service Indicator
                    if !game.isCompleted {
                        HStack {
                            Image(systemName: "tennisball.fill")
                                .foregroundColor(game.currentServer == SharedTeam.team1 ? .blue : .red)
                                .font(.caption)

                            Text("\(game.currentServer == SharedTeam.team1 ? match.team1Name : match.team2Name) serves")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                        .padding(.top, 8)
                    }
                    
                    if game.isCompleted {
                        Text("Game Complete")
                            .font(.caption)
                            .foregroundColor(.green)
                            .padding(.top, 8)
                    }
                } else {
                    Text("Waiting for game to start...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding()
                }
            }
            .padding()
        }
        .navigationTitle("Live Match")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private func addPoint(to team: SharedTeam) {
        // Provide haptic feedback based on team
        if team == SharedTeam.team1 {
            WKInterfaceDevice.current().play(.success)
        } else {
            WKInterfaceDevice.current().play(.failure)
        }

        // Send score update to iPhone
        watchConnectivityManager.sendScoreUpdate(team: team)

        print("📱 Watch: Point added for \(team.rawValue)")
    }
}

#Preview {
    let sampleGame = SharedGame(
        gameNumber: 1,
        team1Score: 8,
        team2Score: 6,
        currentServer: SharedTeam.team1,
        isCompleted: false
    )

    let sampleMatch = SharedLiveMatch(
        id: "sample",
        type: SharedMatchType.singles,
        status: SharedMatchStatus.inProgress,
        bestOfGames: 3,
        team1Name: "John",
        team2Name: "Jane",
        currentGame: sampleGame,
        gameNumber: 1,
        team1GamesWon: 0,
        team2GamesWon: 0
    )

    return LiveMatchWatchView(match: sampleMatch)
        .environmentObject(WatchConnectivityManager())
}
