//
//  SharedModels.swift
//  Shared
//
//  Created by <PERSON><PERSON> on 25/07/2025.
//

import Foundation

// MARK: - Shared Enums

public enum SharedTeam: String, CaseIterable, Codable {
    case team1 = "team1"
    case team2 = "team2"
}

public enum SharedMatchType: String, CaseIterable, Codable {
    case singles = "singles"
    case doubles = "doubles"
    case mixMatch = "mixMatch"
}

public enum SharedMatchStatus: String, CaseIterable, Codable {
    case scheduled = "scheduled"
    case inProgress = "inProgress"
    case completed = "completed"
}

// MARK: - Shared Data Models for Watch Communication

public struct SharedLiveMatch: Codable {
    public let id: String
    public let type: SharedMatchType
    public let status: SharedMatchStatus
    public let bestOfGames: Int
    public let team1Name: String
    public let team2Name: String
    public let currentGame: SharedGame?
    public let gameNumber: Int
    public let team1GamesWon: Int
    public let team2GamesWon: Int
    
    public init(id: String, type: Shared<PERSON><PERSON>Type, status: SharedMatchStatus, bestOfGames: Int, team1Name: String, team2Name: String, currentGame: SharedGame?, gameNumber: Int, team1GamesWon: Int, team2GamesWon: Int) {
        self.id = id
        self.type = type
        self.status = status
        self.bestOfGames = bestOfGames
        self.team1Name = team1Name
        self.team2Name = team2Name
        self.currentGame = currentGame
        self.gameNumber = gameNumber
        self.team1GamesWon = team1GamesWon
        self.team2GamesWon = team2GamesWon
    }
    
    public func toDictionary() -> [String: Any] {
        var dict: [String: Any] = [
            "id": id,
            "type": type.rawValue,
            "status": status.rawValue,
            "bestOfGames": bestOfGames,
            "team1Name": team1Name,
            "team2Name": team2Name,
            "gameNumber": gameNumber,
            "team1GamesWon": team1GamesWon,
            "team2GamesWon": team2GamesWon
        ]
        
        if let currentGame = currentGame {
            dict["currentGame"] = currentGame.toDictionary()
        }
        
        return dict
    }
    
    public static func fromDictionary(_ dict: [String: Any]) -> SharedLiveMatch? {
        guard let id = dict["id"] as? String,
              let typeString = dict["type"] as? String,
              let type = SharedMatchType(rawValue: typeString),
              let statusString = dict["status"] as? String,
              let status = SharedMatchStatus(rawValue: statusString),
              let bestOfGames = dict["bestOfGames"] as? Int,
              let team1Name = dict["team1Name"] as? String,
              let team2Name = dict["team2Name"] as? String,
              let gameNumber = dict["gameNumber"] as? Int,
              let team1GamesWon = dict["team1GamesWon"] as? Int,
              let team2GamesWon = dict["team2GamesWon"] as? Int else {
            return nil
        }
        
        var currentGame: SharedGame?
        if let gameDict = dict["currentGame"] as? [String: Any] {
            currentGame = SharedGame.fromDictionary(gameDict)
        }
        
        return SharedLiveMatch(
            id: id,
            type: type,
            status: status,
            bestOfGames: bestOfGames,
            team1Name: team1Name,
            team2Name: team2Name,
            currentGame: currentGame,
            gameNumber: gameNumber,
            team1GamesWon: team1GamesWon,
            team2GamesWon: team2GamesWon
        )
    }
}

public struct SharedGame: Codable {
    public let gameNumber: Int
    public let team1Score: Int
    public let team2Score: Int
    public let currentServer: SharedTeam
    public let isCompleted: Bool
    
    public init(gameNumber: Int, team1Score: Int, team2Score: Int, currentServer: SharedTeam, isCompleted: Bool) {
        self.gameNumber = gameNumber
        self.team1Score = team1Score
        self.team2Score = team2Score
        self.currentServer = currentServer
        self.isCompleted = isCompleted
    }
    
    public func toDictionary() -> [String: Any] {
        return [
            "gameNumber": gameNumber,
            "team1Score": team1Score,
            "team2Score": team2Score,
            "currentServer": currentServer.rawValue,
            "isCompleted": isCompleted
        ]
    }
    
    public static func fromDictionary(_ dict: [String: Any]) -> SharedGame? {
        guard let gameNumber = dict["gameNumber"] as? Int,
              let team1Score = dict["team1Score"] as? Int,
              let team2Score = dict["team2Score"] as? Int,
              let serverString = dict["currentServer"] as? String,
              let currentServer = SharedTeam(rawValue: serverString),
              let isCompleted = dict["isCompleted"] as? Bool else {
            return nil
        }
        
        return SharedGame(
            gameNumber: gameNumber,
            team1Score: team1Score,
            team2Score: team2Score,
            currentServer: currentServer,
            isCompleted: isCompleted
        )
    }
}

// MARK: - Watch Communication Messages

public struct WatchMessage {
    public static let addPointAction = "addPoint"
    public static let liveMatchUpdateAction = "liveMatchUpdate"
    public static let liveMatchEndedAction = "liveMatchEnded"
    
    public static func addPointMessage(team: SharedTeam) -> [String: Any] {
        return [
            "action": addPointAction,
            "team": team.rawValue
        ]
    }
    
    public static func liveMatchUpdateMessage(match: SharedLiveMatch) -> [String: Any] {
        return [
            "action": liveMatchUpdateAction,
            "match": match.toDictionary()
        ]
    }
    
    public static func liveMatchEndedMessage() -> [String: Any] {
        return [
            "action": liveMatchEndedAction
        ]
    }
}
