//
//  ContentView.swift
//  Table Tennis Watch App
//
//  Created by <PERSON><PERSON> on 25/07/2025.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject var watchConnectivityManager: WatchConnectivityManager

    var body: some View {
        NavigationView {
            VStack {
                if let liveMatch = watchConnectivityManager.currentLiveMatch {
                    LiveMatchWatchView(match: liveMatch)
                } else {
                    VStack(spacing: 20) {
                        Image(systemName: "sportscourt")
                            .font(.system(size: 40))
                            .foregroundColor(.blue)
                        
                        Text("Table Tennis")
                            .font(.headline)
                        
                        Text("No active match")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text("Start a match on your iPhone to begin scoring")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                }
            }
            .navigationTitle("Table Tennis")
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(WatchConnectivityManager())
}
