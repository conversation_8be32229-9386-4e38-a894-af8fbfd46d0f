//
//  Table_Tennis_Watch_AppApp.swift
//  Table Tennis Watch App
//
//  Created by <PERSON><PERSON> on 25/07/2025.
//

import SwiftUI

@main
struct Table_Tennis_Watch_App: App {
    @StateObject private var watchConnectivityManager = WatchConnectivityManager()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(watchConnectivityManager)
        }
    }
}
