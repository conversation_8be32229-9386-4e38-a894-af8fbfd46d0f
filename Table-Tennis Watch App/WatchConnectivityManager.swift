//
//  WatchConnectivityManager.swift
//  Table Tennis Watch App
//
//  Created by <PERSON><PERSON> on 25/07/2025.
//

import Foundation
import WatchConnectivity
import SwiftUI

class WatchConnectivityManager: NSObject, ObservableObject {
    @Published var currentLiveMatch: SharedLiveMatch?
    @Published var isConnected = false

    override init() {
        super.init()

        if WCSession.isSupported() {
            let session = WCSession.default
            session.delegate = self
            session.activate()
        }
    }

    func sendScoreUpdate(team: SharedTeam) {
        guard WCSession.default.isReachable else {
            print("📱 Watch: iPhone not reachable")
            return
        }

        let message = WatchMessage.addPointMessage(team: team)

        WCSession.default.sendMessage(message, replyHandler: { reply in
            print("📱 Watch: Score update sent successfully")
        }) { error in
            print("📱 Watch: Error sending score update: \(error.localizedDescription)")
        }
    }
}

extension WatchConnectivityManager: WCSessionDelegate {
    func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: Error?) {
        DispatchQueue.main.async {
            self.isConnected = activationState == .activated
        }
        
        if let error = error {
            print("📱 Watch: Session activation failed: \(error.localizedDescription)")
        } else {
            print("📱 Watch: Session activated successfully")
        }
    }
    
    func session(_ session: WCSession, didReceiveMessage message: [String : Any]) {
        print("📱 Watch: Received message: \(message)")

        if let action = message["action"] as? String {
            switch action {
            case WatchMessage.liveMatchUpdateAction:
                handleLiveMatchUpdate(message)
            case WatchMessage.liveMatchEndedAction:
                handleLiveMatchEnded()
            default:
                print("📱 Watch: Unknown action: \(action)")
            }
        }
    }

    private func handleLiveMatchUpdate(_ message: [String: Any]) {
        guard let matchData = message["match"] as? [String: Any] else {
            print("📱 Watch: No match data in message")
            return
        }

        DispatchQueue.main.async {
            self.currentLiveMatch = SharedLiveMatch.fromDictionary(matchData)
        }
    }

    private func handleLiveMatchEnded() {
        DispatchQueue.main.async {
            self.currentLiveMatch = nil
        }
    }
}
