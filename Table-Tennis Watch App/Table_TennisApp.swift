//
//  Table_TennisApp.swift
//  Table-Tennis Watch App
//
//  Created by <PERSON><PERSON> on 25/07/2025.
//

import SwiftUI

@main
struct Table_Tennis_Watch_AppApp: App {
    @StateObject private var watchConnectivityManager = WatchConnectivityManager()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(watchConnectivityManager)
        }
    }
}
