// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		C8BD3C572E33ACCF00FD0E78 /* Table-Tennis Watch App.app in Embed Watch Content */ = {isa = PBXBuildFile; fileRef = C8BD3C4A2E33ACCD00FD0E78 /* Table-Tennis Watch App.app */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		C8BD3C6D2E33ADFD00FD0E78 /* WatchConnectivity.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C8BD3C6C2E33ADFD00FD0E78 /* WatchConnectivity.framework */; };
		C8C1B1EE2E0412B80099D76F /* flic2lib in Frameworks */ = {isa = PBXBuildFile; productRef = C8C1B1ED2E0412B80099D76F /* flic2lib */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		C8BD3C552E33ACCF00FD0E78 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = C89C9DD12E02DF7600E0B8EC /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = C8BD3C492E33ACCD00FD0E78;
			remoteInfo = "Table-Tennis Watch App";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		C8BD3C5B2E33ACCF00FD0E78 /* Embed Watch Content */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "$(CONTENTS_FOLDER_PATH)/Watch";
			dstSubfolderSpec = 16;
			files = (
				C8BD3C572E33ACCF00FD0E78 /* Table-Tennis Watch App.app in Embed Watch Content */,
			);
			name = "Embed Watch Content";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		C89C9DD92E02DF7600E0B8EC /* Table Tennis.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Table Tennis.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		C8BBBD7F2E0ACD31006B7125 /* Table Tennis.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Table Tennis.entitlements"; sourceTree = "<group>"; };
		C8BD3C4A2E33ACCD00FD0E78 /* Table-Tennis Watch App.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Table-Tennis Watch App.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		C8BD3C6C2E33ADFD00FD0E78 /* WatchConnectivity.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WatchConnectivity.framework; path = Platforms/WatchOS.platform/Developer/SDKs/WatchOS11.0.sdk/System/Library/Frameworks/WatchConnectivity.framework; sourceTree = DEVELOPER_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		C89C9DDB2E02DF7600E0B8EC /* TableTennis */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TableTennis;
			sourceTree = "<group>";
		};
		C8BD3C4B2E33ACCD00FD0E78 /* Table-Tennis Watch App */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "Table-Tennis Watch App";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		C89C9DD62E02DF7600E0B8EC /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C8C1B1EE2E0412B80099D76F /* flic2lib in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C8BD3C472E33ACCD00FD0E78 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				C8BD3C6D2E33ADFD00FD0E78 /* WatchConnectivity.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		C89C9DD02E02DF7600E0B8EC = {
			isa = PBXGroup;
			children = (
				C8BBBD7F2E0ACD31006B7125 /* Table Tennis.entitlements */,
				C89C9DDB2E02DF7600E0B8EC /* TableTennis */,
				C8BD3C4B2E33ACCD00FD0E78 /* Table-Tennis Watch App */,
				C8BD3C6B2E33ADFC00FD0E78 /* Frameworks */,
				C89C9DDA2E02DF7600E0B8EC /* Products */,
			);
			sourceTree = "<group>";
		};
		C89C9DDA2E02DF7600E0B8EC /* Products */ = {
			isa = PBXGroup;
			children = (
				C89C9DD92E02DF7600E0B8EC /* Table Tennis.app */,
				C8BD3C4A2E33ACCD00FD0E78 /* Table-Tennis Watch App.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		C8BD3C6B2E33ADFC00FD0E78 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C8BD3C6C2E33ADFD00FD0E78 /* WatchConnectivity.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		C89C9DD82E02DF7600E0B8EC /* Table Tennis */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C89C9DE72E02DF7800E0B8EC /* Build configuration list for PBXNativeTarget "Table Tennis" */;
			buildPhases = (
				C89C9DD52E02DF7600E0B8EC /* Sources */,
				C89C9DD62E02DF7600E0B8EC /* Frameworks */,
				C89C9DD72E02DF7600E0B8EC /* Resources */,
				C8BD3C5B2E33ACCF00FD0E78 /* Embed Watch Content */,
			);
			buildRules = (
			);
			dependencies = (
				C8BD3C562E33ACCF00FD0E78 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				C89C9DDB2E02DF7600E0B8EC /* TableTennis */,
			);
			name = "Table Tennis";
			packageProductDependencies = (
				C8C1B1ED2E0412B80099D76F /* flic2lib */,
			);
			productName = "Table Tennis";
			productReference = C89C9DD92E02DF7600E0B8EC /* Table Tennis.app */;
			productType = "com.apple.product-type.application";
		};
		C8BD3C492E33ACCD00FD0E78 /* Table-Tennis Watch App */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C8BD3C582E33ACCF00FD0E78 /* Build configuration list for PBXNativeTarget "Table-Tennis Watch App" */;
			buildPhases = (
				C8BD3C462E33ACCD00FD0E78 /* Sources */,
				C8BD3C472E33ACCD00FD0E78 /* Frameworks */,
				C8BD3C482E33ACCD00FD0E78 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				C8BD3C4B2E33ACCD00FD0E78 /* Table-Tennis Watch App */,
			);
			name = "Table-Tennis Watch App";
			packageProductDependencies = (
			);
			productName = "Table-Tennis Watch App";
			productReference = C8BD3C4A2E33ACCD00FD0E78 /* Table-Tennis Watch App.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		C89C9DD12E02DF7600E0B8EC /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1600;
				LastUpgradeCheck = 1600;
				TargetAttributes = {
					C89C9DD82E02DF7600E0B8EC = {
						CreatedOnToolsVersion = 16.0;
					};
					C8BD3C492E33ACCD00FD0E78 = {
						CreatedOnToolsVersion = 16.0;
					};
				};
			};
			buildConfigurationList = C89C9DD42E02DF7600E0B8EC /* Build configuration list for PBXProject "Table Tennis" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = C89C9DD02E02DF7600E0B8EC;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				C8C1B1EC2E0412B80099D76F /* XCRemoteSwiftPackageReference "flic2lib-ios" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = C89C9DDA2E02DF7600E0B8EC /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				C89C9DD82E02DF7600E0B8EC /* Table Tennis */,
				C8BD3C492E33ACCD00FD0E78 /* Table-Tennis Watch App */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		C89C9DD72E02DF7600E0B8EC /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C8BD3C482E33ACCD00FD0E78 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		C89C9DD52E02DF7600E0B8EC /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		C8BD3C462E33ACCD00FD0E78 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		C8BD3C562E33ACCF00FD0E78 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = C8BD3C492E33ACCD00FD0E78 /* Table-Tennis Watch App */;
			targetProxy = C8BD3C552E33ACCF00FD0E78 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		C89C9DE52E02DF7800E0B8EC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		C89C9DE62E02DF7800E0B8EC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		C89C9DE82E02DF7800E0B8EC /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Table Tennis.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 6;
				DEVELOPMENT_ASSET_PATHS = "\"TableTennis/Preview Content\"";
				DEVELOPMENT_TEAM = 9RD2ZC9927;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Table-Tennis-Info.plist";
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "Deze app gebruikt Bluetooth om verbinding te maken met Flic buttons voor het bijhouden van scores tijdens live wedstrijden.";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "Deze app gebruikt Bluetooth om verbinding te maken met Flic buttons voor het bijhouden van scores tijdens live wedstrijden.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = nl.joostlaurman.TableTennis;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = NO;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		C89C9DE92E02DF7800E0B8EC /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = YES;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Table Tennis.entitlements";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 6;
				DEVELOPMENT_ASSET_PATHS = "\"TableTennis/Preview Content\"";
				DEVELOPMENT_TEAM = 9RD2ZC9927;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "Table-Tennis-Info.plist";
				INFOPLIST_KEY_NSBluetoothAlwaysUsageDescription = "Deze app gebruikt Bluetooth om verbinding te maken met Flic buttons voor het bijhouden van scores tijdens live wedstrijden.";
				INFOPLIST_KEY_NSBluetoothPeripheralUsageDescription = "Deze app gebruikt Bluetooth om verbinding te maken met Flic buttons voor het bijhouden van scores tijdens live wedstrijden.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = nl.joostlaurman.TableTennis;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = NO;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		C8BD3C592E33ACCF00FD0E78 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "app-icon";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Table-Tennis Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 9RD2ZC9927;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = nl.joostlaurman.TableTennis;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = nl.joostlaurman.TableTennis.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 9.6;
			};
			name = Debug;
		};
		C8BD3C5A2E33ACCF00FD0E78 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = "app-icon";
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"Table-Tennis Watch App/Preview Content\"";
				DEVELOPMENT_TEAM = 9RD2ZC9927;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				INFOPLIST_KEY_WKCompanionAppBundleIdentifier = nl.joostlaurman.TableTennis;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = nl.joostlaurman.TableTennis.watchkitapp;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = watchos;
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 4;
				WATCHOS_DEPLOYMENT_TARGET = 9.6;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		C89C9DD42E02DF7600E0B8EC /* Build configuration list for PBXProject "Table Tennis" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C89C9DE52E02DF7800E0B8EC /* Debug */,
				C89C9DE62E02DF7800E0B8EC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C89C9DE72E02DF7800E0B8EC /* Build configuration list for PBXNativeTarget "Table Tennis" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C89C9DE82E02DF7800E0B8EC /* Debug */,
				C89C9DE92E02DF7800E0B8EC /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		C8BD3C582E33ACCF00FD0E78 /* Build configuration list for PBXNativeTarget "Table-Tennis Watch App" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C8BD3C592E33ACCF00FD0E78 /* Debug */,
				C8BD3C5A2E33ACCF00FD0E78 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		C8C1B1EC2E0412B80099D76F /* XCRemoteSwiftPackageReference "flic2lib-ios" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/50ButtonsEach/flic2lib-ios.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 1.4.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		C8C1B1ED2E0412B80099D76F /* flic2lib */ = {
			isa = XCSwiftPackageProductDependency;
			package = C8C1B1EC2E0412B80099D76F /* XCRemoteSwiftPackageReference "flic2lib-ios" */;
			productName = flic2lib;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = C89C9DD12E02DF7600E0B8EC /* Project object */;
}
