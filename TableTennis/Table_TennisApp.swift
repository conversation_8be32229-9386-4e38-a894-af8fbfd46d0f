//
//  Table_TennisApp.swift
//  Table Tennis
//
//  Created by <PERSON><PERSON> on 18/06/2025.
//

import SwiftUI
import UIKit

// AppDelegate for handling orientation lock
class AppDelegate: NSObject, UIApplicationDelegate {
    static var orientationLock = UIInterfaceOrientationMask.all

    func application(_ application: UIApplication, supportedInterfaceOrientationsFor window: UIWindow?) -> UIInterfaceOrientationMask {
        return AppDelegate.orientationLock
    }
}

@main
struct Table_TennisApp: App {
    @UIApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var dataManager = DataManager()
    @StateObject private var themeManager = ThemeManager()
    @StateObject private var flicManager = FlicManager()
    @StateObject private var cloudKitManager = CloudKitManager.shared
    @StateObject private var joinRequestManager = JoinRequestManager(cloudKitManager: CloudKitManager.shared)
    @StateObject private var watchConnectivityManager = iPhoneWatchConnectivityManager()

    var body: some Scene {
        WindowGroup {
            RootView()
                .environmentObject(dataManager)
                .environmentObject(themeManager)
                .environmentObject(flicManager)
                .environmentObject(cloudKitManager)
                .environmentObject(joinRequestManager)
                .environmentObject(watchConnectivityManager)
                .preferredColorScheme(themeManager.currentTheme.colorScheme)
                .onAppear {
                    // Set up the connection between managers
                    watchConnectivityManager.setDataManager(dataManager)
                }
        }
    }
}
