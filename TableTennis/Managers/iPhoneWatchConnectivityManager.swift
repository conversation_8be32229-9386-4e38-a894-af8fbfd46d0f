//
//  iPhoneWatchConnectivityManager.swift
//  Table Tennis
//
//  Created by <PERSON><PERSON> on 25/07/2025.
//

import Foundation
import WatchConnectivity

class iPhoneWatchConnectivityManager: NSObject, ObservableObject {
    @Published var isWatchConnected = false
    @Published var isWatchAppInstalled = false
    
    weak var dataManager: DataManager?
    private var currentLiveMatchId: UUID?
    
    override init() {
        super.init()
        
        if WCSession.isSupported() {
            let session = WCSession.default
            session.delegate = self
            session.activate()
        }
    }
    
    func setDataManager(_ dataManager: DataManager) {
        self.dataManager = dataManager
    }
    
    // MARK: - Live Match Management
    
    func startLiveMatch(_ match: Match) {
        currentLiveMatchId = match.id
        sendLiveMatchUpdate(match)
        print("📱 iPhone: Started live match for Watch: \(match.id)")
    }
    
    func updateLiveMatch(_ match: Match) {
        guard currentLiveMatchId == match.id else { return }
        sendLiveMatchUpdate(match)
        print("📱 iPhone: Updated live match for Watch: \(match.id)")
    }
    
    func endLiveMatch() {
        currentLiveMatchId = nil
        sendLiveMatchEnded()
        print("📱 iPhone: Ended live match for Watch")
    }
    
    // MARK: - Private Methods
    
    private func sendLiveMatchUpdate(_ match: Match) {
        guard WCSession.default.isReachable else {
            print("📱 iPhone: Watch not reachable")
            return
        }
        
        let sharedMatch = convertToSharedMatch(match)
        let message = WatchMessage.liveMatchUpdateMessage(match: sharedMatch)
        
        WCSession.default.sendMessage(message, replyHandler: { reply in
            print("📱 iPhone: Live match update sent successfully")
        }) { error in
            print("📱 iPhone: Error sending live match update: \(error.localizedDescription)")
        }
    }
    
    private func sendLiveMatchEnded() {
        guard WCSession.default.isReachable else {
            print("📱 iPhone: Watch not reachable")
            return
        }
        
        let message = WatchMessage.liveMatchEndedMessage()
        
        WCSession.default.sendMessage(message, replyHandler: { reply in
            print("📱 iPhone: Live match ended message sent successfully")
        }) { error in
            print("📱 iPhone: Error sending live match ended message: \(error.localizedDescription)")
        }
    }
    
    private func convertToSharedMatch(_ match: Match) -> SharedLiveMatch {
        // Find current active game
        let currentGame = match.games.first { !$0.isCompleted }
        let gameNumber = currentGame?.gameNumber ?? (match.games.count + 1)
        
        // Convert current game to shared format
        var sharedCurrentGame: SharedGame?
        if let game = currentGame {
            let sharedTeam: SharedTeam = game.currentServer == .team1 ? .team1 : .team2
            sharedCurrentGame = SharedGame(
                gameNumber: game.gameNumber,
                team1Score: game.team1Score,
                team2Score: game.team2Score,
                currentServer: sharedTeam,
                isCompleted: game.isCompleted
            )
        }
        
        // Calculate games won
        let completedGames = match.games.filter { $0.isCompleted }
        let team1GamesWon = completedGames.filter { $0.winner == .team1 }.count
        let team2GamesWon = completedGames.filter { $0.winner == .team2 }.count
        
        // Get team names
        let team1Name = getTeamDisplayName(for: .team1, match: match)
        let team2Name = getTeamDisplayName(for: .team2, match: match)
        
        // Convert match type
        let sharedType: SharedMatchType
        switch match.type {
        case .singles:
            sharedType = .singles
        case .doubles:
            sharedType = .doubles
        case .mixMatch:
            sharedType = .mixMatch
        }
        
        // Convert match status
        let sharedStatus: SharedMatchStatus
        switch match.status {
        case .scheduled:
            sharedStatus = .scheduled
        case .inProgress:
            sharedStatus = .inProgress
        case .completed:
            sharedStatus = .completed
        case .cancelled:
            sharedStatus = .completed // Treat cancelled as completed for Watch
        }
        
        return SharedLiveMatch(
            id: match.id.uuidString,
            type: sharedType,
            status: sharedStatus,
            bestOfGames: match.bestOfGames,
            team1Name: team1Name,
            team2Name: team2Name,
            currentGame: sharedCurrentGame,
            gameNumber: gameNumber,
            team1GamesWon: team1GamesWon,
            team2GamesWon: team2GamesWon
        )
    }
    
    private func getTeamDisplayName(for team: Team, match: Match) -> String {
        switch team {
        case .team1:
            if match.type == .doubles, let player2 = match.team1Player2 {
                return "\(match.team1Player1.name) & \(player2.name)"
            } else {
                return match.team1Player1.name
            }
        case .team2:
            if match.type == .doubles, let player2 = match.team2Player2 {
                return "\(match.team2Player1.name) & \(player2.name)"
            } else {
                return match.team2Player1.name
            }
        }
    }
}

extension iPhoneWatchConnectivityManager: WCSessionDelegate {
    func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: Error?) {
        DispatchQueue.main.async {
            self.isWatchConnected = activationState == .activated && session.isPaired
            self.isWatchAppInstalled = session.isWatchAppInstalled
        }
        
        if let error = error {
            print("📱 iPhone: Session activation failed: \(error.localizedDescription)")
        } else {
            print("📱 iPhone: Session activated successfully")
            print("📱 iPhone: Watch paired: \(session.isPaired)")
            print("📱 iPhone: Watch app installed: \(session.isWatchAppInstalled)")
        }
    }
    
    func sessionDidBecomeInactive(_ session: WCSession) {
        print("📱 iPhone: Session became inactive")
    }
    
    func sessionDidDeactivate(_ session: WCSession) {
        print("📱 iPhone: Session deactivated")
        session.activate()
    }
    
    func session(_ session: WCSession, didReceiveMessage message: [String : Any]) {
        print("📱 iPhone: Received message from Watch: \(message)")
        
        if let action = message["action"] as? String {
            switch action {
            case WatchMessage.addPointAction:
                handleAddPointFromWatch(message)
            default:
                print("📱 iPhone: Unknown action from Watch: \(action)")
            }
        }
    }
    
    private func handleAddPointFromWatch(_ message: [String: Any]) {
        guard let teamString = message["team"] as? String,
              let sharedTeam = SharedTeam(rawValue: teamString),
              let currentMatchId = currentLiveMatchId,
              let dataManager = dataManager,
              let match = dataManager.match(withId: currentMatchId) else {
            print("📱 iPhone: Invalid add point message from Watch")
            return
        }
        
        // Convert SharedTeam to Team
        let team: Team = sharedTeam == .team1 ? .team1 : .team2
        
        DispatchQueue.main.async {
            // Find the current active game
            guard let gameIndex = match.games.firstIndex(where: { !$0.isCompleted }) else {
                print("📱 iPhone: No active game found for Watch point")
                return
            }
            
            print("📱 iPhone: Adding point from Watch for \(team) to game \(gameIndex + 1)")
            
            // Create a mutable copy of the match
            var updatedMatch = match
            updatedMatch.games[gameIndex].addPoint(to: team, matchType: match.type)
            
            // Update the match in DataManager
            dataManager.updateMatch(updatedMatch)
            
            print("📱 iPhone: Point added from Watch successfully")
        }
    }
}
